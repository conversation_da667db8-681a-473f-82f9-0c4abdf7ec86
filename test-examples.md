# 智能404重定向测试用例

## 功能说明
新的404页面现在可以智能识别旧的URL格式并重定向到对应的新页面。

## 测试用例

### 1. 旧格式URL测试
以下URL应该能够自动重定向到对应的新页面：

- `https://playunb.com/g/58b54832/celeste` → `https://playunb.com/game/celeste/`
- `https://playunb.com/g/2675b77e/falling-ball` → `https://playunb.com/game/falling-ball/`
- `https://playunb.com/g/12345/super-mario-bros` → `https://playunb.com/game/super-mario-bros/`
- `https://playunb.com/g/abcdef/pokemon-emerald` → `https://playunb.com/game/pokemon-emerald/`

### 2. 简化URL测试
直接游戏名称也应该能够重定向：

- `https://playunb.com/celeste` → `https://playunb.com/game/celeste/`
- `https://playunb.com/falling-ball` → `https://playunb.com/game/falling-ball/`
- `https://playunb.com/mario` → `https://playunb.com/game/super-mario-bros/` (最相似匹配)
- `https://playunb.com/pokemon` → `https://playunb.com/game/pokemon-emerald/` (最相似匹配)

### 3. 模糊匹配测试
即使名称不完全匹配，也应该能找到最相似的游戏：

- `https://playunb.com/slope` → `https://playunb.com/game/slope/`
- `https://playunb.com/chrome-dino` → `https://playunb.com/game/chrome-dino/`
- `https://playunb.com/dino` → `https://playunb.com/game/chrome-dino/`

### 4. 无匹配情况
如果找不到合适的匹配，会重定向到首页：

- `https://playunb.com/nonexistent-game` → `https://playunb.com/` (3秒后)
- `https://playunb.com/g/123/invalid-game-name` → `https://playunb.com/` (3秒后)

## 工作原理

1. **URL解析**: 从URL中提取游戏名称
   - 支持 `/g/{id}/{game-name}` 格式
   - 支持 `/game/{game-name}` 格式  
   - 支持 `/{game-name}` 格式

2. **字符串匹配**: 使用Levenshtein距离算法计算相似度
   - 清理特殊字符和连字符
   - 计算多种变体的相似度
   - 优先考虑子字符串匹配

3. **智能重定向**:
   - 相似度 > 60% 才会重定向到匹配的游戏
   - 找到匹配时：显示匹配的游戏名称和相似度，5秒倒计时自动重定向
   - 没有匹配时：3秒倒计时自动重定向到首页
   - 提供立即跳转按钮

## 用户体验

- 用户访问旧URL时会看到友好的重定向页面
- 找到匹配时：显示匹配游戏和相似度，5秒倒计时
- 没有匹配时：显示友好提示，3秒倒计时跳转首页
- 提供立即跳转按钮和浏览所有游戏的链接

## 开发调试

在开发环境中，控制台会显示详细的匹配过程：
- 提取的游戏名称
- 最佳匹配结果
- 相似度百分比
- 最终重定向URL
