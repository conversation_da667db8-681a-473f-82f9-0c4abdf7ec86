'use client';

import React, { useEffect, useState } from 'react';
import { useController } from '@/context/ControllerContext';

interface NESControllerProps {
  runner?: string;
}

export default function NESController({ runner }: NESControllerProps) {
  // 所有hooks必须在组件顶层调用，不能在条件语句之后
  const { isVisible, simulateKeyEvent } = useController();
  const [isMounted, setIsMounted] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);

  // Ensure component is mounted on client side
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // 检测全屏状态
  useEffect(() => {
    const handleFullscreenChange = () => {
      try {
        setIsFullscreen(!!document.fullscreenElement);
      } catch (error) {
        console.warn('Error detecting fullscreen state:', error);
      }
    };

    if (typeof document !== 'undefined') {
      document.addEventListener('fullscreenchange', handleFullscreenChange);
      return () => document.removeEventListener('fullscreenchange', handleFullscreenChange);
    }
  }, []);

  // 条件返回必须在所有hooks之后
  if (!isMounted || !isVisible || runner !== 'EMULATOR_NES') return null;

  const handlePress = (keyCode: number) => {
    try {
      simulateKeyEvent(keyCode, 'keydown');
    } catch (error) {
      console.error('Error in handlePress:', error);
    }
  };

  const handleRelease = (keyCode: number) => {
    try {
      simulateKeyEvent(keyCode, 'keyup');
    } catch (error) {
      console.error('Error in handleRelease:', error);
    }
  };

  const buttonProps = (keyCode: number) => ({
    onTouchStart: (e: React.TouchEvent) => {
      e.preventDefault();
      handlePress(keyCode);
    },
    onTouchEnd: (e: React.TouchEvent) => {
      e.preventDefault();
      handleRelease(keyCode);
    },
    onMouseDown: (e: React.MouseEvent) => {
      e.preventDefault();
      handlePress(keyCode);
    },
    onMouseUp: (e: React.MouseEvent) => {
      e.preventDefault();
      handleRelease(keyCode);
    },
    onMouseLeave: (e: React.MouseEvent) => {
      e.preventDefault();
      handleRelease(keyCode);
    }
  });

  const containerClass = isFullscreen
    ? 'absolute bottom-8 left-0 right-0 z-50 bg-black bg-opacity-90 p-4 md:bottom-12'
    : 'fixed bottom-4 left-1/2 transform -translate-x-1/2 z-[9999] w-full max-w-lg px-4';

  return (
    <div className={containerClass}>
      <div className={`
        ${isFullscreen
          ? 'flex items-center justify-center max-w-5xl mx-auto bg-black bg-opacity-95 rounded-lg'
          : 'bg-black bg-opacity-80 backdrop-blur-sm rounded-lg'
        }
        p-4 sm:p-6 flex items-center justify-between gap-6 sm:gap-10 md:gap-12 w-full
      `}>
        {/* D-Pad */}
        <div className="grid grid-cols-3 gap-1 sm:gap-2">
          <div />
          <button
            {...buttonProps(38)}
            className="w-10 h-10 sm:w-12 sm:h-12 md:w-14 md:h-14 bg-gray-700 hover:bg-gray-600 active:bg-blue-500 text-white rounded flex items-center justify-center font-bold text-base sm:text-lg"
          >
            ↑
          </button>
          <div />
          <button
            {...buttonProps(37)}
            className="w-10 h-10 sm:w-12 sm:h-12 md:w-14 md:h-14 bg-gray-700 hover:bg-gray-600 active:bg-blue-500 text-white rounded flex items-center justify-center font-bold text-base sm:text-lg"
          >
            ←
          </button>
          <div className="w-10 h-10 sm:w-12 sm:h-12 md:w-14 md:h-14" />
          <button
            {...buttonProps(39)}
            className="w-10 h-10 sm:w-12 sm:h-12 md:w-14 md:h-14 bg-gray-700 hover:bg-gray-600 active:bg-blue-500 text-white rounded flex items-center justify-center font-bold text-base sm:text-lg"
          >
            →
          </button>
          <div />
          <button
            {...buttonProps(40)}
            className="w-10 h-10 sm:w-12 sm:h-12 md:w-14 md:h-14 bg-gray-700 hover:bg-gray-600 active:bg-blue-500 text-white rounded flex items-center justify-center font-bold text-base sm:text-lg"
          >
            ↓
          </button>
          <div />
        </div>

        {/* Select/Start */}
        <div className="flex flex-col gap-2 sm:gap-3">
          <button
            {...buttonProps(17)}
            className="px-3 py-2 sm:px-4 sm:py-2 md:px-5 md:py-3 bg-gray-700 hover:bg-gray-600 active:bg-blue-500 text-white rounded text-xs sm:text-sm font-bold min-w-[60px] sm:min-w-[70px]"
          >
            SELECT
          </button>
          <button
            {...buttonProps(13)}
            className="px-3 py-2 sm:px-4 sm:py-2 md:px-5 md:py-3 bg-gray-700 hover:bg-gray-600 active:bg-blue-500 text-white rounded text-xs sm:text-sm font-bold min-w-[60px] sm:min-w-[70px]"
          >
            START
          </button>
        </div>

        {/* Action buttons */}
        <div className="flex gap-3 sm:gap-4">
          <button
            {...buttonProps(90)}
            className="w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16 bg-gray-700 hover:bg-gray-600 active:bg-red-500 text-white rounded-full flex items-center justify-center font-bold text-base sm:text-lg md:text-xl"
          >
            B
          </button>
          <button
            {...buttonProps(88)}
            className="w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16 bg-gray-700 hover:bg-gray-600 active:bg-red-500 text-white rounded-full flex items-center justify-center font-bold text-base sm:text-lg md:text-xl"
          >
            A
          </button>
        </div>
      </div>
    </div>
  );
}