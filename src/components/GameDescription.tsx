import { Game } from '@/types/Game';

interface GameDescriptionProps {
  game: Game;
}

export default function GameDescription({ game }: GameDescriptionProps) {
  // 如果没有描述，不显示组件
  if (!game.description) {
    return null;
  }

  return (
    <div className="mt-4 sm:mt-6 p-4 sm:p-6 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
      <div className="flex items-center gap-2 mb-3">
        <span className="text-xl" role="img" aria-hidden="true">
          📖
        </span>
        <h3 className="text-base sm:text-lg font-semibold text-gray-900 dark:text-white">
          About {game.name}
        </h3>
      </div>
      
      <div className="prose prose-sm sm:prose-base dark:prose-invert max-w-none">
        <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
          {game.description}
        </p>
      </div>

      {/* 游戏类型和特性标签 */}
      <div className="flex flex-wrap gap-2 mt-4 pt-3 border-t border-gray-200 dark:border-gray-600">
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
          {game.runner === 'UNITY' ? 'Unity WebGL' :
           game.runner === 'IFRAME' ? 'HTML5 Game' :
           game.runner === 'EMULATOR_NES' ? 'NES Classic' :
           game.runner === 'EMULATOR_GBA' ? 'GBA Retro' :
           game.runner === 'RUFFLE' ? 'Flash Game' :
           'Browser Game'}
        </span>
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
          Free to Play
        </span>
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
          No Download Required
        </span>
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200">
          Unblocked
        </span>
      </div>
    </div>
  );
}
